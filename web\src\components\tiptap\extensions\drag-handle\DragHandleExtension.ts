import { Extension } from '@tiptap/core'
import { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state'
import { DragHandleView } from './DragHandleView'

export interface DragHandleOptions {
  enabled?: boolean
}

export const DragHandleExtension = Extension.create<DragHandleOptions>({
  name: 'dragHandle',

  addOptions() {
    return {
      enabled: true,
    }
  },

  addProseMirrorPlugins() {
    if (!this.options.enabled) return []

    const view = new DragHandleView({
      editor: this.editor,
    })

    return [
      new Plugin({
        key: new PluginKey(`${this.name}-drag-handle`),
        view: () => ({
          destroy: () => view.destroy()
        }),
        props: {
          handleDOMEvents: view.events()
        },
      }),
    ]
  },
})
        codeBlock: {
          name: 'codeBlock',
          selector: '.code-block, pre, code[class*="language-"]',
          canDragInto: ['paragraph', 'codeBlock', 'listItem'],
          canReceiveFrom: ['paragraph', 'codeBlock'],
          preserveAttributes: ['language', 'class', 'id', 'data-*', 'style'],
          nestingRules: {
            allowedParents: ['doc', 'listItem', 'blockquote', 'tableCell'],
            allowedChildren: ['text']
          }
        },
        blockquote: {
          name: 'blockquote',
          selector: 'blockquote',
          canDragInto: ['paragraph', 'blockquote', 'listItem'],
          canReceiveFrom: ['paragraph', 'heading', 'blockquote'],
          preserveAttributes: ['class', 'id', 'data-*', 'style'],
          nestingRules: {
            allowedParents: ['doc', 'listItem'],
            allowedChildren: ['paragraph', 'heading', 'bulletList', 'orderedList']
          }
        },
        bulletList: {
          name: 'bulletList',
          selector: 'ul',
          canDragInto: ['paragraph', 'listItem', 'bulletList'],
          canReceiveFrom: ['bulletList', 'orderedList'],
          preserveAttributes: ['class', 'id', 'data-*', 'style'],
          nestingRules: {
            maxDepth: 6,
            allowedParents: ['doc', 'listItem', 'blockquote'],
            allowedChildren: ['listItem']
          }
        },
        orderedList: {
          name: 'orderedList',
          selector: 'ol',
          canDragInto: ['paragraph', 'listItem', 'orderedList'],
          canReceiveFrom: ['bulletList', 'orderedList'],
          preserveAttributes: ['start', 'class', 'id', 'data-*', 'style'],
          nestingRules: {
            maxDepth: 6,
            allowedParents: ['doc', 'listItem', 'blockquote'],
            allowedChildren: ['listItem']
          }
        }
      } as Readonly<Record<string, any>>,
      dragHandleSelector: '.drag-handle',
      ghostImageOpacity: 0.5,
      animationDuration: 200,
      keyboardShortcuts: {
        'Alt-ArrowUp': 'moveNodeUp',
        'Alt-ArrowDown': 'moveNodeDown',
        'Alt-Shift-ArrowUp': 'moveToTop',
        'Alt-Shift-ArrowDown': 'moveToBottom',
        'Ctrl-d': 'duplicateNode',
        'Delete': 'deleteNode'
      } as Readonly<Record<string, string>>,
      announceToScreenReader: true,
      respectReducedMotion: true,
      ariaLabels: {
        dragHandle: 'Drag to reorder',
        moveUp: 'Move up',
        moveDown: 'Move down'
      } as Readonly<Record<string, string>>,
      keyboardHelp: true
    }
  },

  addStorage() {
    return {
      dragState: null,
      isDragging: false,
      currentDragElement: null
    }
  },

  addCommands() {
    return {
      moveParagraphUp: () => ({ state, dispatch, tr }: CommandProps) => {
        return this.editor.commands.moveNodeUp('paragraph')
      },

      moveParagraphDown: () => ({ state, dispatch, tr }: CommandProps) => {
        return this.editor.commands.moveNodeDown('paragraph')
      },

      moveNodeUp: (nodeType?: string) => ({ state, dispatch, tr }: CommandProps) => {
        if (!dispatch) return false

        const { selection } = state
        const { $from } = selection
        
        // Find the current block node
        let depth = $from.depth
        while (depth > 0) {
          const node = $from.node(depth)
          const isSupported = this.options.supportedNodes.includes(node.type.name)
          const isTargetType = !nodeType || node.type.name === nodeType
          
          if (isSupported && isTargetType) break
          depth--
        }
        
        if (depth === 0) return false
        
        const currentNode = $from.node(depth)
        const currentPos = $from.start(depth) - 1
        
        // Validate node type configuration
        const nodeConfig = this.options.nodeTypeConfigs[currentNode.type.name]
        if (!nodeConfig) return false
        
        // Find the previous sibling
        const parent = $from.node(depth - 1)
        const currentIndex = $from.index(depth - 1)
        
        if (currentIndex === 0) return false // Already at the top
        
        const prevNode = parent.child(currentIndex - 1)
        
        // Validate nesting rules
        if (!NodeValidationUtils.areNodesCompatible(currentNode, prevNode, nodeConfig, this.options.nodeTypeConfigs[prevNode.type.name])) {
          return false
        }
        
        const prevPos = $from.start(depth - 1) + parent.child(0, currentIndex - 1).nodeSize
        
        // Preserve node attributes and content
        const preservedNode = NodeValidationUtils.createPreservedNode(currentNode, NodeValidationUtils.preserveNodeContent(currentNode, nodeConfig))
        
        // Create transaction to swap nodes
        const newTr = tr
          .delete(currentPos, currentPos + currentNode.nodeSize)
          .insert(prevPos, preservedNode)
        
        dispatch(newTr)
        return true
      },

      moveNodeDown: (nodeType?: string) => ({ state, dispatch, tr }: CommandProps) => {
        if (!dispatch) return false

        const { selection } = state
        const { $from } = selection
        
        // Find the current block node
        let depth = $from.depth
        while (depth > 0) {
          const node = $from.node(depth)
          const isSupported = this.options.supportedNodes.includes(node.type.name)
          const isTargetType = !nodeType || node.type.name === nodeType
          
          if (isSupported && isTargetType) break
          depth--
        }
        
        if (depth === 0) return false
        
        const currentNode = $from.node(depth)
        const currentPos = $from.start(depth) - 1
        
        // Validate node type configuration
        const nodeConfig = this.options.nodeTypeConfigs[currentNode.type.name]
        if (!nodeConfig) return false
        
        // Find the next sibling
        const parent = $from.node(depth - 1)
        const currentIndex = $from.index(depth - 1)
        
        if (currentIndex >= parent.childCount - 1) return false // Already at the bottom
        
        const nextNode = parent.child(currentIndex + 1)
        
        // Validate nesting rules
        if (!NodeValidationUtils.areNodesCompatible(currentNode, nextNode, nodeConfig, this.options.nodeTypeConfigs[nextNode.type.name])) {
          return false
        }
        
        const nextPos = currentPos + currentNode.nodeSize
        
        // Preserve node attributes and content
        const preservedNode = NodeValidationUtils.createPreservedNode(currentNode, NodeValidationUtils.preserveNodeContent(currentNode, nodeConfig))
        
        // Create transaction to swap nodes
        const newTr = tr
          .delete(nextPos, nextPos + nextNode.nodeSize)
          .insert(currentPos, nextNode)
          .delete(currentPos, currentPos + preservedNode.nodeSize)
          .insert(nextPos, preservedNode)
        
        dispatch(newTr)
        return true
      },

      moveToTop: (nodeType?: string) => ({ state, dispatch, tr }: CommandProps) => {
        if (!dispatch) return false

        const { selection } = state
        const { $from } = selection
        
        // Find the current block node
        let depth = $from.depth
        while (depth > 0) {
          const node = $from.node(depth)
          const isSupported = this.options.supportedNodes.includes(node.type.name)
          const isTargetType = !nodeType || node.type.name === nodeType
          
          if (isSupported && isTargetType) break
          depth--
        }
        
        if (depth === 0) return false
        
        const currentNode = $from.node(depth)
        const currentPos = $from.start(depth) - 1
        const parent = $from.node(depth - 1)
        const currentIndex = $from.index(depth - 1)
        
        if (currentIndex === 0) return false // Already at top
        
        // Preserve node content
        const nodeConfig = this.options.nodeTypeConfigs[currentNode.type.name]
        if (!nodeConfig) return false
        
        const preservedNode = NodeValidationUtils.createPreservedNode(currentNode, NodeValidationUtils.preserveNodeContent(currentNode, nodeConfig))
        
        // Move to top of parent
        const newTr = tr
          .delete(currentPos, currentPos + currentNode.nodeSize)
          .insert($from.start(depth - 1), preservedNode)
        
        dispatch(newTr)
        return true
      },

      moveToBottom: (nodeType?: string) => ({ state, dispatch, tr }: CommandProps) => {
        if (!dispatch) return false

        const { selection } = state
        const { $from } = selection
        
        // Find the current block node
        let depth = $from.depth
        while (depth > 0) {
          const node = $from.node(depth)
          const isSupported = this.options.supportedNodes.includes(node.type.name)
          const isTargetType = !nodeType || node.type.name === nodeType
          
          if (isSupported && isTargetType) break
          depth--
        }
        
        if (depth === 0) return false
        
        const currentNode = $from.node(depth)
        const currentPos = $from.start(depth) - 1
        const parent = $from.node(depth - 1)
        const currentIndex = $from.index(depth - 1)
        
        if (currentIndex >= parent.childCount - 1) return false // Already at bottom
        
        // Preserve node content
        const nodeConfig = this.options.nodeTypeConfigs[currentNode.type.name]
        if (!nodeConfig) return false
        
        const preservedNode = NodeValidationUtils.createPreservedNode(currentNode, NodeValidationUtils.preserveNodeContent(currentNode, nodeConfig))
        
        // Move to bottom of parent
        const newTr = tr
          .delete(currentPos, currentPos + currentNode.nodeSize)
          .insert($from.end(depth - 1), preservedNode)
        
        dispatch(newTr)
        return true
      },

      // Enhanced keyboard shortcuts commands
      convertToHeading1: () => ({ commands }: CommandProps) => {
        return commands.setHeading({ level: 1 })
      },

      convertToHeading2: () => ({ commands }: CommandProps) => {
        return commands.setHeading({ level: 2 })
      },

      convertToHeading3: () => ({ commands }: CommandProps) => {
        return commands.setHeading({ level: 3 })
      },

      convertToHeading: () => ({ commands }: CommandProps) => {
        return commands.setHeading({ level: 1 })
      },

      convertToParagraph: () => ({ commands }: CommandProps) => {
        return commands.setParagraph()
      },

      convertToList: () => ({ commands }: CommandProps) => {
        return commands.toggleBulletList()
      },

      convertToCodeBlock: () => ({ commands }: CommandProps) => {
        return commands.setCodeBlock()
      },

      convertToBlockquote: () => ({ commands }: CommandProps) => {
        return commands.setBlockquote()
      },

      duplicateNode: () => ({ state, dispatch, tr }: CommandProps) => {
        if (!dispatch) return false

        const { selection } = state
        const { $from } = selection
        
        // Find the current block node
        let depth = $from.depth
        while (depth > 0) {
          const node = $from.node(depth)
          if (this.options.supportedNodes.includes(node.type.name)) break
          depth--
        }
        
        if (depth === 0) return false
        
        const currentNode = $from.node(depth)
        const currentPos = $from.start(depth) - 1
        
        // Preserve node content
        const nodeConfig = this.options.nodeTypeConfigs[currentNode.type.name]
        if (!nodeConfig) return false
        
        const preservedNode = NodeValidationUtils.createPreservedNode(currentNode, NodeValidationUtils.preserveNodeContent(currentNode, nodeConfig))
        
        // Insert duplicate after current node
        const newTr = tr.insert(currentPos + currentNode.nodeSize, preservedNode)
        
        dispatch(newTr)
        return true
      },

      deleteNode: () => ({ state, dispatch, tr }: CommandProps) => {
        if (!dispatch) return false

        const { selection } = state
        const { $from } = selection
        
        // Find the current block node
        let depth = $from.depth
        while (depth > 0) {
          const node = $from.node(depth)
          if (this.options.supportedNodes.includes(node.type.name)) break
          depth--
        }
        
        if (depth === 0) return false
        
        const currentNode = $from.node(depth)
        const currentPos = $from.start(depth) - 1
        
        // Delete the node
        const newTr = tr.delete(currentPos, currentPos + currentNode.nodeSize)
        
        dispatch(newTr)
        return true
      },

      showClickMenu: () => ({ commands }: CommandProps) => {
        // This will be handled by the ClickMenuExtension
        return commands.showClickMenu?.() || false
      }
    }
  },

  addKeyboardShortcuts() {
    if (!this.options.enabled) return {}
    
    return Object.entries(this.options.keyboardShortcuts).reduce(
      (shortcuts, [key, command]) => {
        shortcuts[key] = () => {
          const commandFn = this.editor.commands[command as keyof typeof this.editor.commands]
          if (typeof commandFn === 'function') {
            return commandFn()
          }
          return false
        }
        return shortcuts
      },
      {} as Record<string, () => boolean>
    )
  },

  addProseMirrorPlugins() {
    if (!this.options.enabled) return []
    
    return [
      new DragHandlePlugin({
        ...this.options,
        editor: this.editor
      })
    ]
  },

  onCreate() {
    // Initialize drag handle storage
    this.storage.dragState = null
    this.storage.isDragging = false
    this.storage.currentDragElement = null
  },

  onDestroy() {
    // Performance monitoring cleanup
    PerformanceMonitoring.MetricsCollector.getInstance().cleanup()
    
    // Memory management cleanup
    PerformanceUtils.MemoryManager.cleanup(this)
    
    // Cleanup any active drag operations
    if (this.storage.isDragging) {
      this.storage.isDragging = false
      this.storage.currentDragElement = null
      this.storage.dragState = null
    }
  },

  /**
   * Validate if a node can be moved relative to another node
   */
  validateNodeMovement(currentNode: any, siblingNode: any, config: any): boolean {
    const siblingConfig = this.options.nodeTypeConfigs[siblingNode.type.name]
    
    if (!config || !siblingConfig) return false
    
    // Use validation utility to check compatibility
    return NodeValidationUtils.areNodesCompatible(
      currentNode, siblingNode, config, siblingConfig
    )
  },

  /**
   * Validate nesting rules for a node
   */
  validateNestingRules(node: any, config: any, $pos: any): any {
    return NodeValidationUtils.validateNesting(node, config, $pos)
  },

  /**
   * Preserve node content and attributes during movement
   */
  preserveNodeContent(node: any, config: any): any {
    const preservation = NodeValidationUtils.preserveNodeContent(node, config)
    return NodeValidationUtils.createPreservedNode(node, preservation)
  }
})