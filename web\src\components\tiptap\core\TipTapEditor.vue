<template>
  <div
    v-if="editor"
    class="tiptap-editor-wrapper"
    :class="{ 'tiptap-fullscreen': isFullscreen }"
    style="width: 100%"
  >
    <!-- 全屏模式下的关闭按钮 -->
    <div v-if="isFullscreen" class="fullscreen-close-button" @click="handleCloseFullscreen">
      <IosClose :size="24" />
    </div>

    <EditorToolbar
      v-if="props.toolbar"
      :editor="editor"
      :extensions-set="extensionsSet"
      :toolbar-class="toolbarClass"
      :external-fullscreen-state="toolbarFullscreenState"
      :modal="modal"
      @image-upload="imageInputRef?.click()"
      @show-modal="handleShowModal"
      @toggle-fullscreen="handleToggleFullscreen"
    />

    <EditorBubbleMenu
      v-if="editor && props.bubbleMenu"
      :editor="editor"
      :extensions-set="extensionsSet"
      :select-bubble-menu="selectBubbleMenu"
      @show-modal="handleShowModal"
    />

    <EditorFloatingMenu
      v-if="editor && props.floatingMenu"
      :editor="editor"
      :extensions-set="extensionsSet"
      @show-modal="handleShowModal"
      @image-upload="imageInputRef?.click()"
    />

    <!-- Click Menu -->
    <ClickMenuView
      v-if="editor"
      :editor="editor"
      :menu-state="editor.storage.clickMenu?.menuState || null"
      :menu-items="getClickMenuItems()"
      :keyboard-navigation="true"
      @item-execute="handleClickMenuItemExecute"
      @menu-hide="handleClickMenuHide"
    />

    <div class="editor-content" :class="{ 'editor-readonly': !props.editable }" style="width: 100%">
      <EditorContent :editor="editor" />
      <div v-if="props.showCharacterCount && editor" class="character-count">
        {{ editor.storage.characterCount.characters }} / {{ props.characterLimit }}
      </div>
    </div>

    <!-- 模态框处理组件 -->
    <EditorModalHandler v-model:modal="modal" :editor="editor" />
    <input
      type="file"
      :accept="fileApi.imageTypes.join(',')"
      ref="imageInputRef"
      @change="(e) => editor && handleImageChange(e, editor, props.fileBucket, props.useThumbnail)"
      class="display-none"
    />
  </div>
</template>

<script lang="ts" setup>
import { EditorContent } from '@tiptap/vue-3'
import { IosClose } from '@/icons'

import { onMounted, onUnmounted } from 'vue'

import fileApi from '@/api/file'
import '@/components/tiptap/core/styles/index.scss'
import { useEditor } from '@/components/tiptap/core/editor/EditorCore'
import { useEditorEvents } from '@/components/tiptap/events/EditorEventManager'
import { useFullscreen } from '@/components/tiptap/extensions/fullscreen'
import { useImageUpload } from '@/components/tiptap/extensions/image/useImageUpload'
import EditorBubbleMenu from '@/components/tiptap/menu/EditorBubbleMenu.vue'
import EditorFloatingMenu from '@/components/tiptap/menu/EditorFloatingMenu.vue'
import ClickMenuView from '@/components/tiptap/extensions/click-menu/ClickMenuView.vue'
import { useModal } from '@/components/tiptap/modal/EditorModal'
import EditorModalHandler from '@/components/tiptap/modal/EditorModalHandler.vue'
// 导入可复用逻辑
import EditorToolbar from '@/components/tiptap/toolbar/EditorToolbar.vue'
import { DEFAULT_CHARACTER_LIMIT } from '@/constants/tiptap.constants'
import type { EditorWithFormatPainter } from '@/types/tiptap.types'
import type { ClickMenuItem } from '@/components/tiptap/extensions/click-menu/types'

interface BubbleMenuState {
  image: boolean
  bilibili: boolean
}

const props = defineProps({
  modelValue: {
    type: [Object, String],
    default: '',
  },
  extensions: {
    type: Array as () => string[],
    default: () => [],
  },
  allExtensions: {
    type: Boolean,
    default: false,
  },
  toolbar: {
    type: Boolean,
    default: false,
  },
  placeholder: {
    type: String,
    default: '...',
  },
  editable: {
    type: Boolean,
    default: true,
  },
  fileBucket: {
    type: String,
    required: true,
  },
  bubbleMenu: {
    type: Boolean,
    default: false,
  },
  floatingMenu: {
    type: Boolean,
    default: false,
  },
  editorProps: {
    type: Object,
    default: () => ({
      attributes: {
        class: 'ProseMirror',
      },
    }),
  },
  toolbarClass: {
    type: Object,
    default: ['editor-toolbar'],
  },
  showCharacterCount: {
    type: Boolean,
    default: false,
  },
  characterLimit: {
    type: Number,
    default: DEFAULT_CHARACTER_LIMIT,
  },
  useThumbnail: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits<{
  (event: 'update:modelValue', content: unknown): void
  (event: 'save'): void
}>()

// 使用可复用逻辑
const { imageInputRef, handleImageChange, imageHandleCallback } = useImageUpload()
const { modal, handleShowModal } = useModal()

// 定义图片上传触发器和模态框触发器
const imageUploadTrigger = () => imageInputRef.value?.click()
const modalTrigger = (title: string, callback: () => void, needInput?: boolean) => {
  handleShowModal({ title, trigger: callback, onlyInputValue: needInput })
}

const { editor, initEditor, watchEditable, clearContent, setContent, getMarkdown, cleanupEditor } =
  useEditor(
    { ...props, modelValue: typeof props.modelValue === 'string' ? {} : props.modelValue },
    emit,
    imageUploadTrigger,
    modalTrigger,
  ) as { editor: { value: EditorWithFormatPainter | undefined } } & ReturnType<typeof useEditor>
const {
  isFullscreen,
  toolbarFullscreenState,
  handleToggleFullscreen,
  handleCloseFullscreen,
  cleanupFullscreen,
} = useFullscreen(editor)

// 初始化编辑器
let extensionsSet: Set<string>
let selectBubbleMenu: BubbleMenuState = {
  image: false,
  bilibili: false,
}

onMounted(() => {
  // 初始化编辑器并获取扩展集
  extensionsSet = initEditor()

  // 配置编辑器事件处理
  const editorEvents = useEditorEvents(
    editor,
    imageHandleCallback,
    props.fileBucket,
    props.useThumbnail,
  )

  // 更新 selectBubbleMenu 状态
  if (editorEvents.selectBubbleMenu) {
    selectBubbleMenu = editorEvents.selectBubbleMenu
  }

  // 设置编辑器事件
  editorEvents.setupEditorEvents()

  // 监听可编辑状态变化
  watchEditable()
})

// 暴露保存方法给父组件使用
const handleSave = () => {
  emit('save')
}

// Click Menu 事件处理
const getClickMenuItems = (): ClickMenuItem[] => {
  if (!editor.value) return []
  
  // 从扩展配置中获取菜单项
  const clickMenuExtension = editor.value.extensionManager.extensions.find(ext => ext.name === 'clickMenu')
  return clickMenuExtension?.options?.menuItems || []
}

const handleClickMenuItemExecute = (item: ClickMenuItem) => {
  console.log('Click menu item executed:', item.label)
}

const handleClickMenuHide = () => {
  if (editor.value) {
    editor.value.commands.hideClickMenu()
  }
}

// 模态框处理逻辑已移至 EditorModalHandler 组件

// 暴露API
defineExpose({
  setContent,
  clearContent,
  getMarkdown,
  editor,
  handleSave,
})

// 清理资源
onUnmounted(() => {
  cleanupEditor()
  cleanupFullscreen()
})
</script>

<style scoped>
:deep(.n-color-picker-trigger) {
  border: none !important;
  width: 0 !important;
  height: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  overflow: hidden !important;
}

.fullscreen-close-button {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  z-index: 1000;
  cursor: pointer;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.fullscreen-close-button:hover {
  transform: scale(1.1);
}

.character-count {
  text-align: right;
  font-size: 0.8rem;
  color: #888;
  margin-top: 0.25rem;
  padding-right: 0.5rem;
}
</style>
