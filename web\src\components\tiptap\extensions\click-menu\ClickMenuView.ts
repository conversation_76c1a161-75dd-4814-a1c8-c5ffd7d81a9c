import { Editor } from '@tiptap/core'
import { Node, ResolvedPos } from '@tiptap/pm/model'
import { EditorView } from '@tiptap/pm/view'

export interface ClickMenuViewOptions {
  editor: Editor
}

export interface ClickMenuActiveOptions {
  node: Node
  pos: ResolvedPos
  dom: HTMLElement
}

export class ClickMenuView {
  private readonly editor: Editor
  private readonly element: HTMLElement
  private readonly options: ClickMenuViewOptions
  private _timer: number | undefined
  private _active: ClickMenuActiveOptions | undefined
  private _visible: boolean = false

  constructor(options: ClickMenuViewOptions) {
    this.editor = options.editor
    this.options = options
    this.element = this._createElement()
    this._attachToEditor()
  }

  public show(active: ClickMenuActiveOptions) {
    this._active = active
    this._visible = true
    
    // Position the click menu
    const rect = active.dom.getBoundingClientRect()
    const editorRect = this.editor.view.dom.getBoundingClientRect()
    
    this.element.style.display = 'block'
    this.element.style.position = 'absolute'
    this.element.style.left = `${rect.left - editorRect.left - 40}px`
    this.element.style.top = `${rect.top - editorRect.top}px`
    this.element.style.zIndex = '1000'
  }

  public hide() {
    this.element.style.display = 'none'
    this._active = undefined
    this._visible = false
  }

  public destroy() {
    this.element.remove()
  }

  public events(): Record<string, (view: EditorView, event: Event) => boolean> {
    return {
      keydown: () => {
        this.hide()
        return false
      },
      click: (view, event) => {
        // Hide menu when clicking elsewhere
        if (!this.element.contains(event.target as HTMLElement)) {
          this.hide()
        }
        return false
      },
      mousemove: (view, event) => {
        if (view.composing || !view.editable || !event.target) {
          return false
        }

        clearTimeout(this._timer)
        this._timer = window.setTimeout(() => {
          const active = this._findActiveNode(event)
          if (active && this._shouldShowMenu(active)) {
            this.show(active)
          } else {
            this.hide()
          }
        }, 100)
        return false
      },
    }
  }

  private _createElement() {
    const element = document.createElement('div')
    element.classList.add('click-menu')
    element.style.display = 'none'
    
    // Create plus button
    const plusButton = document.createElement('button')
    plusButton.innerHTML = `
      <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
        <path d="M8 2a.5.5 0 0 1 .5.5v5h5a.5.5 0 0 1 0 1h-5v5a.5.5 0 0 1-1 0v-5h-5a.5.5 0 0 1 0-1h5v-5A.5.5 0 0 1 8 2z"/>
      </svg>
    `
    plusButton.classList.add('click-menu__button', 'click-menu__plus')
    plusButton.title = 'Add block'
    
    plusButton.addEventListener('click', () => {
      if (this._active) {
        const { pos, node } = this._active
        this.editor.chain()
          .insertContentAt(pos.pos - (node.isLeaf ? 0 : 1) + node.nodeSize, {
            type: 'paragraph'
          })
          .focus()
          .run()
        this.hide()
      }
    })

    // Create drag button
    const dragButton = document.createElement('button')
    dragButton.innerHTML = `
      <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
        <circle cx="4" cy="4" r="1.5"/>
        <circle cx="12" cy="4" r="1.5"/>
        <circle cx="4" cy="8" r="1.5"/>
        <circle cx="12" cy="8" r="1.5"/>
        <circle cx="4" cy="12" r="1.5"/>
        <circle cx="12" cy="12" r="1.5"/>
      </svg>
    `
    dragButton.classList.add('click-menu__button', 'click-menu__drag')
    dragButton.title = 'Drag to move'
    
    dragButton.addEventListener('click', () => {
      // Show a simple menu with options
      this._showContextMenu()
    })

    element.appendChild(plusButton)
    element.appendChild(dragButton)

    return element
  }

  private _showContextMenu() {
    if (!this._active) return

    const menu = document.createElement('div')
    menu.classList.add('click-menu__context')
    menu.style.position = 'absolute'
    menu.style.background = 'white'
    menu.style.border = '1px solid #ccc'
    menu.style.borderRadius = '4px'
    menu.style.padding = '8px'
    menu.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)'
    menu.style.zIndex = '1001'
    menu.style.left = '100%'
    menu.style.top = '0'

    const options = [
      { label: 'Convert to Heading', action: () => this.editor.commands.setHeading({ level: 1 }) },
      { label: 'Convert to Paragraph', action: () => this.editor.commands.setParagraph() },
      { label: 'Convert to List', action: () => this.editor.commands.toggleBulletList() },
      { label: 'Delete Block', action: () => this._deleteCurrentBlock() },
    ]

    options.forEach(option => {
      const button = document.createElement('button')
      button.textContent = option.label
      button.style.display = 'block'
      button.style.width = '100%'
      button.style.padding = '4px 8px'
      button.style.border = 'none'
      button.style.background = 'none'
      button.style.cursor = 'pointer'
      button.style.textAlign = 'left'
      
      button.addEventListener('click', () => {
        option.action()
        this.hide()
        menu.remove()
      })
      
      button.addEventListener('mouseenter', () => {
        button.style.background = '#f0f0f0'
      })
      
      button.addEventListener('mouseleave', () => {
        button.style.background = 'none'
      })
      
      menu.appendChild(button)
    })

    this.element.appendChild(menu)

    // Remove menu when clicking outside
    const removeMenu = (e: MouseEvent) => {
      if (!menu.contains(e.target as HTMLElement)) {
        menu.remove()
        document.removeEventListener('click', removeMenu)
      }
    }
    
    setTimeout(() => {
      document.addEventListener('click', removeMenu)
    }, 0)
  }

  private _deleteCurrentBlock() {
    if (!this._active) return
    
    const { pos, node } = this._active
    const from = pos.pos - (node.isLeaf ? 0 : 1)
    const to = from + node.nodeSize
    
    this.editor.chain()
      .deleteRange({ from, to })
      .focus()
      .run()
  }

  private _attachToEditor() {
    // Attach the click menu to the editor container
    const editorContainer = this.editor.view.dom.parentElement
    if (editorContainer) {
      editorContainer.style.position = 'relative'
      editorContainer.appendChild(this.element)
    }
  }

  private _findActiveNode(event: MouseEvent): ClickMenuActiveOptions | undefined {
    const { view } = this.editor
    if (view.composing || !view.editable || !event.target || !view.dom.parentElement) {
      return undefined
    }

    let pos = 0
    let node = document.elementFromPoint(event.x + 50, event.y)
    
    if (!node || node === view.dom) {
      node = event.target as Element | null
    }

    if (!node || node === view.dom) {
      node = document.elementFromPoint(event.x, event.y)
    }

    if (node) {
      pos = view.posAtDOM(node, 0)
    }

    if (pos <= 0) {
      return undefined
    }

    let _pos = view.state.doc.resolve(pos)
    let _node = _pos.node()

    if (_node.type.name === 'doc') {
      const node = view.state.doc.nodeAt(pos)
      if (!node) {
        return undefined
      }
      _node = node
    }

    // Find the block node
    while (_node && (!_node.type.isBlock || _node.type.name === 'doc')) {
      _pos = view.state.doc.resolve(_pos.before())
      _node = _pos.node()
    }

    _pos = _pos.pos - _pos.parentOffset === 0 ? _pos : view.state.doc.resolve(_pos.pos - _pos.parentOffset)
    
    let _dom = view.nodeDOM(_pos.pos) as HTMLElement | undefined
    if (!_dom) {
      _dom = view.nodeDOM(_pos.pos - 1) as HTMLElement | undefined
    }

    while (_dom?.parentElement && _dom.parentElement !== view.dom.parentElement && _pos.pos === view.posAtDOM(_dom.parentElement, 0)) {
      _dom = _dom.parentElement
    }

    if (!_dom) {
      return undefined
    }

    return { node: _node, pos: _pos, dom: _dom }
  }

  private _shouldShowMenu(active: ClickMenuActiveOptions): boolean {
    // Only show for certain node types
    const allowedTypes = ['paragraph', 'heading', 'blockquote', 'codeBlock', 'listItem']
    return allowedTypes.includes(active.node.type.name)
  }
}
