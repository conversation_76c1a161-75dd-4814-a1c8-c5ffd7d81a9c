import { Editor } from '@tiptap/core'
import { Node, ResolvedPos } from '@tiptap/pm/model'
import { NodeSelection } from '@tiptap/pm/state'
import { EditorProps } from '@tiptap/pm/view'

export interface DragHandleViewOptions {
  editor: Editor
}

export interface DragHandleActiveOptions {
  node: Node
  pos: ResolvedPos
  dom: HTMLElement
}

export class DragHandleView {
  private readonly editor: Editor
  private readonly element: HTMLElement
  private readonly options: DragHandleViewOptions
  private _timer: number | undefined
  private _active: DragHandleActiveOptions | undefined
  private _dragging: boolean | undefined
  private _selection: NodeSelection | undefined

  constructor(options: DragHandleViewOptions) {
    this.editor = options.editor
    this.options = options
    this.element = this._createElement()
    this._attachToEditor()
  }

  public show(active: DragHandleActiveOptions) {
    this._active = active
    
    // Position the drag handle next to the element
    const rect = active.dom.getBoundingClientRect()
    const editorRect = this.editor.view.dom.getBoundingClientRect()
    
    this.element.style.display = 'block'
    this.element.style.position = 'absolute'
    this.element.style.left = `${rect.left - editorRect.left - 30}px`
    this.element.style.top = `${rect.top - editorRect.top + 2}px`
    this.element.style.zIndex = '10'
  }

  public hide() {
    this.element.style.display = 'none'
    this._active = undefined
  }

  public destroy() {
    this.element.remove()
  }

  public events(): EditorProps['handleDOMEvents'] {
    return {
      drop: () => {
        this._dragging = false
        this.hide()
        return false
      },
      keydown: () => {
        this.hide()
        return false
      },
      dragenter: () => {
        this._dragging = true
        return false
      },
      dragleave: () => {
        this._dragging = false
        return false
      },
      mousemove: (view, event) => {
        if (view.composing || !view.editable || !event.target) {
          return false
        }

        clearTimeout(this._timer)
        this._timer = window.setTimeout(() => {
          const active = this._findActiveNode(event)
          if (active) {
            this.show(active)
          } else {
            this.hide()
          }
        }, 50)
        return false
      },
    }
  }

  private _createElement() {
    const element = document.createElement('div')
    element.classList.add('drag-handle')
    element.style.display = 'none'
    element.innerHTML = `
      <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
        <circle cx="4" cy="4" r="1.5"/>
        <circle cx="12" cy="4" r="1.5"/>
        <circle cx="4" cy="8" r="1.5"/>
        <circle cx="12" cy="8" r="1.5"/>
        <circle cx="4" cy="12" r="1.5"/>
        <circle cx="12" cy="12" r="1.5"/>
      </svg>
    `
    
    element.draggable = true
    
    element.addEventListener('mousedown', () => {
      const active = this._active
      if (active && NodeSelection.isSelectable(active.node)) {
        const { state, view } = this.editor
        const selection = NodeSelection.create(state.doc, active.pos.pos - (active.node.isLeaf ? 0 : 1))
        view.dispatch(state.tr.setSelection(selection))
        view.focus()
        this._selection = selection
      }
    })

    element.addEventListener('dragstart', (e) => {
      this._dragging = true
      const view = this.editor.view
      const selection = this._selection
      
      if (e.dataTransfer && selection) {
        const slice = selection.content()
        view.dragging = { slice, move: true }
        
        // Create a simple text representation for the drag
        const textContent = selection.node.textContent || 'Block'
        
        e.dataTransfer.effectAllowed = 'move'
        e.dataTransfer.clearData()
        e.dataTransfer.setData('text/plain', textContent)
        e.dataTransfer.setData('text/html', `<div>${textContent}</div>`)
      }
    })

    element.addEventListener('dragend', () => {
      this._dragging = false
      this._selection = undefined
      this.hide()
    })

    return element
  }

  private _attachToEditor() {
    // Attach the drag handle to the editor container
    const editorContainer = this.editor.view.dom.parentElement
    if (editorContainer) {
      editorContainer.style.position = 'relative'
      editorContainer.appendChild(this.element)
    }
  }

  private _findActiveNode(event: MouseEvent): DragHandleActiveOptions | undefined {
    const { view } = this.editor
    if (view.composing || !view.editable || !event.target || !view.dom.parentElement) {
      return undefined
    }

    let pos = 0
    let node = document.elementFromPoint(event.x + 50, event.y)
    
    if (!node || node === view.dom) {
      node = event.target as Element | null
    }

    if (!node || node === view.dom) {
      node = document.elementFromPoint(event.x, event.y)
    }

    if (node) {
      pos = view.posAtDOM(node, 0)
    }

    if (pos <= 0) {
      return undefined
    }

    let _pos = view.state.doc.resolve(pos)
    let _node = _pos.node()

    if (_node.type.name === 'doc') {
      const node = view.state.doc.nodeAt(pos)
      if (!node) {
        return undefined
      }
      _node = node
    }

    // Find the block node
    while (_node && (!_node.type.isBlock || _node.type.name === 'doc')) {
      _pos = view.state.doc.resolve(_pos.before())
      _node = _pos.node()
    }

    // Skip if it's the first child of certain nodes
    if (this._nodeIsFirstChild(_pos)) {
      return undefined
    }

    _pos = _pos.pos - _pos.parentOffset === 0 ? _pos : view.state.doc.resolve(_pos.pos - _pos.parentOffset)
    
    let _dom = view.nodeDOM(_pos.pos) as HTMLElement | undefined
    if (!_dom) {
      _dom = view.nodeDOM(_pos.pos - 1) as HTMLElement | undefined
    }

    while (_dom?.parentElement && _dom.parentElement !== view.dom.parentElement && _pos.pos === view.posAtDOM(_dom.parentElement, 0)) {
      _dom = _dom.parentElement
    }

    if (!_dom) {
      return undefined
    }

    return { node: _node, pos: _pos, dom: _dom }
  }

  private _nodeIsFirstChild(pos: ResolvedPos) {
    let parent = pos.parent
    const node = pos.node()
    
    if (parent === node) {
      parent = pos.node(pos.depth - 1)
    }
    
    if (!parent || parent.type.name === 'doc') {
      return false
    }
    
    return parent.firstChild === node
  }
}
